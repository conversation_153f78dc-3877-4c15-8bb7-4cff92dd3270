{"version": 3, "sources": ["../../src/neon-serverless/session.ts"], "sourcesContent": ["import {\n\ttype Client,\n\tPool,\n\ttype PoolClient,\n\ttype QueryArrayConfig,\n\ttype QueryConfig,\n\ttype QueryResult,\n\ttype QueryResultRow,\n\ttypes,\n} from '@neondatabase/serverless';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, type SQL, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport type NeonClient = Pool | PoolClient | Client;\n\nexport class NeonPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'NeonPreparedQuery';\n\n\tprivate rawQueryConfig: QueryConfig;\n\tprivate queryConfig: QueryArrayConfig;\n\n\tconstructor(\n\t\tprivate client: NeonClient,\n\t\tqueryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t\tthis.rawQueryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\ttypes: {\n\t\t\t\t// @ts-ignore\n\t\t\t\tgetTypeParser: (typeId, format) => {\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMPTZ) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMP) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.DATE) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.INTERVAL) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn types.getTypeParser(typeId, format);\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t\tthis.queryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\trowMode: 'array',\n\t\t\ttypes: {\n\t\t\t\t// @ts-ignore\n\t\t\t\tgetTypeParser: (typeId, format) => {\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMPTZ) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.TIMESTAMP) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.DATE) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\tif (typeId === types.builtins.INTERVAL) {\n\t\t\t\t\t\treturn (val: any) => val;\n\t\t\t\t\t}\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn types.getTypeParser(typeId, format);\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\n\t\tconst { fields, client, rawQueryConfig: rawQuery, queryConfig: query, joinsNotNullableMap, customResultMapper } =\n\t\t\tthis;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn client.query(rawQuery, params);\n\t\t}\n\n\t\tconst result = await client.query(query, params);\n\n\t\treturn customResultMapper\n\t\t\t? customResultMapper(result.rows)\n\t\t\t: result.rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\t\treturn this.client.query(this.rawQueryConfig, params).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\t\treturn this.client.query(this.queryConfig, params).then((result) => result.rows);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface NeonSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class NeonSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<NeonQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NeonSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: NeonClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: NeonSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new NeonPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tname,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync query(query: string, params: unknown[]): Promise<QueryResult> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client.query({\n\t\t\trowMode: 'array',\n\t\t\ttext: query,\n\t\t\tvalues: params,\n\t\t});\n\t\treturn result;\n\t}\n\n\tasync queryObjects<T extends QueryResultRow>(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<QueryResult<T>> {\n\t\treturn this.client.query<T>(query, params);\n\t}\n\n\toverride async count(sql: SQL): Promise<number> {\n\t\tconst res = await this.execute<{ rows: [{ count: string }] }>(sql);\n\n\t\treturn Number(\n\t\t\tres['rows'][0]['count'],\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig: PgTransactionConfig = {},\n\t): Promise<T> {\n\t\tconst session = this.client instanceof Pool // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t? new NeonSession(await this.client.connect(), this.dialect, this.schema, this.options)\n\t\t\t: this;\n\t\tconst tx = new NeonTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\tawait tx.execute(sql`begin ${tx.getTransactionConfigSQL(config)}`);\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (error) {\n\t\t\tawait tx.execute(sql`rollback`);\n\t\t\tthrow error;\n\t\t} finally {\n\t\t\tif (this.client instanceof Pool) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t(session.client as PoolClient).release();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class NeonTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<NeonQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NeonTransaction';\n\n\toverride async transaction<T>(transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new NeonTransaction<TFullSchema, TSchema>(this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (e) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow e;\n\t\t}\n\t}\n}\n\nexport interface NeonQueryResultHKT extends PgQueryResultHKT {\n\ttype: QueryResult<Assume<this['row'], QueryResultRow>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBASO;AACP,oBAA2B;AAE3B,oBAA2B;AAE3B,qBAA8B;AAG9B,qBAA2C;AAE3C,iBAA4D;AAC5D,mBAA0C;AAInC,MAAM,0BAAyD,+BAAmB;AAAA,EAMxF,YACS,QACR,aACQ,QACA,QACA,QACR,MACQ,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAT1B;AAEA;AACA;AACA;AAEA;AACA;AAGR,SAAK,iBAAiB;AAAA,MACrB;AAAA,MACA,MAAM;AAAA,MACN,OAAO;AAAA;AAAA,QAEN,eAAe,CAAC,QAAQ,WAAW;AAClC,cAAI,WAAW,wBAAM,SAAS,aAAa;AAC1C,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,wBAAM,SAAS,WAAW;AACxC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,wBAAM,SAAS,MAAM;AACnC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,wBAAM,SAAS,UAAU;AACvC,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,iBAAO,wBAAM,cAAc,QAAQ,MAAM;AAAA,QAC1C;AAAA,MACD;AAAA,IACD;AACA,SAAK,cAAc;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA;AAAA,QAEN,eAAe,CAAC,QAAQ,WAAW;AAClC,cAAI,WAAW,wBAAM,SAAS,aAAa;AAC1C,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,wBAAM,SAAS,WAAW;AACxC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,wBAAM,SAAS,MAAM;AACnC,mBAAO,CAAC,QAAa;AAAA,UACtB;AACA,cAAI,WAAW,wBAAM,SAAS,UAAU;AACvC,mBAAO,CAAC,QAAa;AAAA,UACtB;AAEA,iBAAO,wBAAM,cAAc,QAAQ,MAAM;AAAA,QAC1C;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EA/DA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EA8DR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AAErD,UAAM,EAAE,QAAQ,QAAQ,gBAAgB,UAAU,aAAa,OAAO,qBAAqB,mBAAmB,IAC7G;AACD,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,OAAO,MAAM,UAAU,MAAM;AAAA,IACrC;AAEA,UAAM,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM;AAE/C,WAAO,qBACJ,mBAAmB,OAAO,IAAI,IAC9B,OAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EAC1F;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AACrD,WAAO,KAAK,OAAO,MAAM,KAAK,gBAAgB,MAAM,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EACnF;AAAA,EAEA,OAAO,oBAAyD,CAAC,GAAyB;AACzF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AACrD,WAAO,KAAK,OAAO,MAAM,KAAK,aAAa,MAAM,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAChF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAMO,MAAM,oBAGH,yBAAoD;AAAA,EAK7D,YACS,QACR,SACQ,QACA,UAA8B,CAAC,GACtC;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EAYR,aACC,OACA,QACA,MACA,uBACA,oBACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAM,OAAe,QAAyC;AACnE,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,MAAM;AAAA,MACtC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,aACL,OACA,QAC0B;AAC1B,WAAO,KAAK,OAAO,MAAS,OAAO,MAAM;AAAA,EAC1C;AAAA,EAEA,MAAe,MAAMA,MAA2B;AAC/C,UAAM,MAAM,MAAM,KAAK,QAAuCA,IAAG;AAEjE,WAAO;AAAA,MACN,IAAI,MAAM,EAAE,CAAC,EAAE,OAAO;AAAA,IACvB;AAAA,EACD;AAAA,EAEA,MAAe,YACd,aACA,SAA8B,CAAC,GAClB;AACb,UAAM,UAAU,KAAK,kBAAkB,yBACpC,IAAI,YAAY,MAAM,KAAK,OAAO,QAAQ,GAAG,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,IACpF;AACH,UAAM,KAAK,IAAI,gBAAsC,KAAK,SAAS,SAAS,KAAK,MAAM;AACvF,UAAM,GAAG,QAAQ,uBAAY,GAAG,wBAAwB,MAAM,CAAC,EAAE;AACjE,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,sBAAW;AAC5B,aAAO;AAAA,IACR,SAAS,OAAO;AACf,YAAM,GAAG,QAAQ,wBAAa;AAC9B,YAAM;AAAA,IACP,UAAE;AACD,UAAI,KAAK,kBAAkB,wBAAM;AAChC,QAAC,QAAQ,OAAsB,QAAQ;AAAA,MACxC;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,wBAGH,6BAAwD;AAAA,EACjE,QAA0B,wBAAU,IAAY;AAAA,EAEhD,MAAe,YAAe,aAAoF;AACjH,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI,gBAAsC,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AAClH,UAAM,GAAG,QAAQ,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,GAAG;AACX,YAAM,GAAG,QAAQ,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": ["sql"]}