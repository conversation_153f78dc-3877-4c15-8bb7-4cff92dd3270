{"version": 3, "sources": ["../../src/sql-js/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport type { SQLJsDatabase } from './driver.ts';\n\nexport function migrate<TSchema extends Record<string, unknown>>(\n\tdb: SQLJsDatabase<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\tdb.dialect.migrate(migrations, db.session, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAmC;AAG5B,SAAS,QACf,IACA,QACC;AACD,QAAM,iBAAa,oCAAmB,MAAM;AAC5C,KAAG,QAAQ,QAAQ,YAAY,GAAG,SAAS,MAAM;AAClD;", "names": []}