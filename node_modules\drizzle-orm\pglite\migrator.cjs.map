{"version": 3, "sources": ["../../src/pglite/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport type { PgliteDatabase } from './driver.ts';\n\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: PgliteDatabase<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\tawait db.dialect.migrate(migrations, db.session, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAmC;AAGnC,eAAsB,QACrB,IACA,QACC;AACD,QAAM,iBAAa,oCAAmB,MAAM;AAC5C,QAAM,GAAG,QAAQ,QAAQ,YAAY,GAAG,SAAS,MAAM;AACxD;", "names": []}