{"version": 3, "sources": ["../../src/xata-http/driver.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { createTableRelationsHelpers, extractTablesRelationalConfig } from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport type { XataHttpClient, XataHttpQueryResultHKT } from './session.ts';\nimport { XataHttpSession } from './session.ts';\n\nexport interface XataDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class XataHttpDriver {\n\tstatic readonly [entityKind]: string = 'XataDriver';\n\n\tconstructor(\n\t\tprivate client: XataHttpClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: XataDriverOptions = {},\n\t) {\n\t\tthis.initMappers();\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): XataHttpSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new XataHttpSession(this.client, this.dialect, schema, {\n\t\t\tlogger: this.options.logger,\n\t\t});\n\t}\n\n\tinitMappers() {\n\t\t// TODO: Add custom type parsers\n\t}\n}\n\nexport class XataHttpDatabase<TSchema extends Record<string, unknown> = Record<string, never>>\n\textends PgDatabase<XataHttpQueryResultHKT, TSchema>\n{\n\tstatic override readonly [entityKind]: string = 'XataHttpDatabase';\n\n\t/** @internal */\n\tdeclare readonly session: XataHttpSession<TSchema, ExtractTablesWithRelations<TSchema>>;\n}\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: XataHttpClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): XataHttpDatabase<TSchema> & {\n\t$client: XataHttpClient;\n} {\n\tconst dialect = new PgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(config.schema, createTableRelationsHelpers);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new XataHttpDriver(client, dialect, { logger });\n\tconst session = driver.createSession(schema);\n\n\tconst db = new XataHttpDatabase(\n\t\tdialect,\n\t\tsession,\n\t\tschema as RelationalSchemaConfig<ExtractTablesWithRelations<TSchema>> | undefined,\n\t);\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAE3B,oBAA8B;AAC9B,gBAA2B;AAC3B,qBAA0B;AAE1B,uBAA2E;AAG3E,qBAAgC;AAMzB,MAAM,eAAe;AAAA,EAG3B,YACS,QACA,SACA,UAA6B,CAAC,GACrC;AAHO;AACA;AACA;AAER,SAAK,YAAY;AAAA,EAClB;AAAA,EARA,QAAiB,wBAAU,IAAY;AAAA,EAUvC,cACC,QACmE;AACnE,WAAO,IAAI,+BAAgB,KAAK,QAAQ,KAAK,SAAS,QAAQ;AAAA,MAC7D,QAAQ,KAAK,QAAQ;AAAA,IACtB,CAAC;AAAA,EACF;AAAA,EAEA,cAAc;AAAA,EAEd;AACD;AAEO,MAAM,yBACJ,qBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAIjD;AAEO,SAAS,QACf,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,yBAAU,EAAE,QAAQ,OAAO,OAAO,CAAC;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe,gDAA8B,OAAO,QAAQ,4CAA2B;AAC7F,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,eAAe,QAAQ,SAAS,EAAE,OAAO,CAAC;AAC7D,QAAM,UAAU,OAAO,cAAc,MAAM;AAE3C,QAAM,KAAK,IAAI;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACA,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;", "names": []}