{"version": 3, "sources": ["../../src/tidb-serverless/driver.ts"], "sourcesContent": ["import { type Config, connect, type Connection } from '@tidbcloud/serverless';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { MySqlDatabase } from '~/mysql-core/db.ts';\nimport { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport type { TiDBServerlessPreparedQueryHKT, TiDBServerlessQueryResultHKT } from './session.ts';\nimport { TiDBServerlessSession } from './session.ts';\n\nexport interface TiDBServerlessSDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class TiDBServerlessDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends MySqlDatabase<TiDBServerlessQueryResultHKT, TiDBServerlessPreparedQueryHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'TiDBServerlessDatabase';\n}\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: Connection,\n\tconfig: DrizzleConfig<TSchema> = {},\n): TiDBServerlessDatabase<TSchema> & {\n\t$client: Connection;\n} {\n\tconst dialect = new MySqlDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new TiDBServerlessSession(client, dialect, undefined, schema, { logger });\n\tconst db = new TiDBServerlessDatabase(dialect, session, schema as any, 'default') as TiDBServerlessDatabase<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Connection = Connection,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t& ({\n\t\t\tconnection: string | Config;\n\t\t} | {\n\t\t\tclient: TClient;\n\t\t})\n\t\t& DrizzleConfig<TSchema>,\n\t]\n): TiDBServerlessDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = connect({\n\t\t\turl: params[0],\n\t\t});\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& { connection?: Config | string; client?: TClient }\n\t\t\t& DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tconst instance = typeof connection === 'string'\n\t\t\t? connect({\n\t\t\t\turl: connection,\n\t\t\t})\n\t\t\t: connect(connection!);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): TiDBServerlessDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": "AAAA,SAAsB,eAAgC;AACtD,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAC7B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAA6B,gBAAgB;AAE7C,SAAS,6BAA6B;AAM/B,MAAM,+BAEH,cAAqF;AAAA,EAC9F,QAA0B,UAAU,IAAY;AACjD;AAEA,SAAS,UACR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,aAAa,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC1D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,sBAAsB,QAAQ,SAAS,QAAW,QAAQ,EAAE,OAAO,CAAC;AACxF,QAAM,KAAK,IAAI,uBAAuB,SAAS,SAAS,QAAe,SAAS;AAChF,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;AAEO,SAAS,WAIZ,QAeF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,WAAW,QAAQ;AAAA,MACxB,KAAK,OAAO,CAAC;AAAA,IACd,CAAC;AAED,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAIzD,QAAI;AAAQ,aAAO,UAAU,QAAQ,aAAa;AAElD,UAAM,WAAW,OAAO,eAAe,WACpC,QAAQ;AAAA,MACT,KAAK;AAAA,IACN,CAAC,IACC,QAAQ,UAAW;AAEtB,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUA,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["drizzle"]}