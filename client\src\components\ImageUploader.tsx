import { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { Upload, Link as LinkIcon, Clipboard } from "lucide-react";
import { SampleGallery } from "./SampleGallery";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface ImageUploaderProps {
  language: Language;
  onUploadStart: (file: File) => void;
  onUploadComplete: (processId: number) => void;
}

export function ImageUploader({ language, onUploadStart, onUploadComplete }: ImageUploaderProps) {
  const { t } = useTranslation(language);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Supported image formats
  const SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff',
    'image/tif',
    'image/svg+xml'
  ];

  const SUPPORTED_IMAGE_EXTENSIONS = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.bmp',
    '.tiff',
    '.tif',
    '.svg'
  ];

  const handleFileSelect = useCallback(async (file: File) => {
    // Check MIME type
    const isSupportedMimeType = SUPPORTED_IMAGE_TYPES.includes(file.type.toLowerCase());

    // Check file extension as fallback
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const isSupportedExtension = SUPPORTED_IMAGE_EXTENSIONS.includes(fileExtension);

    // Also check if mimetype starts with 'image/' for broader compatibility
    const isImageMimeType = file.type.startsWith('image/');

    if (!(isSupportedMimeType || (isImageMimeType && isSupportedExtension))) {
      toast({
        title: "Invalid file type",
        description: `Please select a supported image format: ${SUPPORTED_IMAGE_EXTENSIONS.join(', ')}`,
        variant: "destructive",
      });
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image smaller than 10MB",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    onUploadStart(file);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await apiRequest('POST', '/api/upload', formData);
      const result = await response.json();

      onUploadComplete(result.id);
      
      toast({
        title: "Upload successful",
        description: "Your image is being processed",
      });
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "Please try again",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [onUploadStart, onUploadComplete, toast]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handlePasteFromClipboard = useCallback(async () => {
    try {
      const clipboardItems = await navigator.clipboard.read();
      for (const item of clipboardItems) {
        if (item.types.includes('image/png') || item.types.includes('image/jpeg')) {
          const blob = await item.getType(item.types.find(type => type.startsWith('image/'))!);
          const file = new File([blob], 'clipboard-image.png', { type: blob.type });
          handleFileSelect(file);
          return;
        }
      }
      toast({
        title: "No image found",
        description: "Please copy an image to your clipboard first",
        variant: "destructive",
      });
    } catch (error) {
      toast({
        title: "Clipboard access failed",
        description: "Please grant clipboard permissions or use file upload",
        variant: "destructive",
      });
    }
  }, [handleFileSelect, toast]);

  return (
    <div className="max-w-2xl mx-auto mb-12 animate-slide-up" style={{ animationDelay: '0.4s' }}>
      <div
        className={`upload-area rounded-2xl p-12 text-center cursor-pointer transition-all duration-300 ${
          isDragOver ? 'dragover' : ''
        } ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="mb-6">
          <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
            <Upload className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-2xl font-semibold mb-2">{t("upload.title")}</h3>
          <p className="text-gray-600">{t("upload.description")}</p>
        </div>
        
        <Button
          className="gradient-bg text-white font-semibold px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1 mb-4"
          disabled={isUploading}
        >
          <Upload className="h-4 w-4 mr-2" />
          {isUploading ? "Uploading..." : t("upload.button")}
        </Button>
        
        <div className="flex justify-center items-center space-x-4 text-sm text-gray-600">
          <Button
            variant="ghost"
            size="sm"
            className="hover:text-purple-600 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Implement URL paste functionality
              toast({
                title: "Feature coming soon",
                description: "URL upload will be available soon",
              });
            }}
          >
            <LinkIcon className="h-4 w-4 mr-1" />
            {t("upload.url")}
          </Button>
          <span>•</span>
          <Button
            variant="ghost"
            size="sm"
            className="hover:text-purple-600 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              handlePasteFromClipboard();
            }}
          >
            <Clipboard className="h-4 w-4 mr-1" />
            {t("upload.clipboard")}
          </Button>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept=".jpg,.jpeg,.png,.gif,.webp,.bmp,.tiff,.tif,.svg,image/*"
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>
      
      <SampleGallery language={language} onSampleSelect={handleFileSelect} />
      
      <p className="text-xs text-gray-600 text-center mt-6">
        {t("terms.notice")} <a href="/terms" className="text-purple-600 hover:underline">{t("terms.tos")}</a>.{" "}
        {t("terms.privacy.notice")} <a href="/privacy" className="text-purple-600 hover:underline">{t("terms.privacy")}</a>.
      </p>
    </div>
  );
}
