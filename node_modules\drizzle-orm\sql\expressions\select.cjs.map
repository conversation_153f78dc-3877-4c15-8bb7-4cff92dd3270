{"version": 3, "sources": ["../../../src/sql/expressions/select.ts"], "sourcesContent": ["import type { AnyColumn } from '../../column.ts';\nimport type { SQL, SQLWrapper } from '../sql.ts';\nimport { sql } from '../sql.ts';\n\n/**\n * Used in sorting, this specifies that the given\n * column or expression should be sorted in ascending\n * order. By the SQL standard, ascending order is the\n * default, so it is not usually necessary to specify\n * ascending sort order.\n *\n * ## Examples\n *\n * ```ts\n * // Return cars, starting with the oldest models\n * // and going in ascending order to the newest.\n * db.select().from(cars)\n *   .orderBy(asc(cars.year));\n * ```\n *\n * @see desc to sort in descending order\n */\nexport function asc(column: AnyColumn | SQLWrapper): SQL {\n\treturn sql`${column} asc`;\n}\n\n/**\n * Used in sorting, this specifies that the given\n * column or expression should be sorted in descending\n * order.\n *\n * ## Examples\n *\n * ```ts\n * // Select users, with the most recently created\n * // records coming first.\n * db.select().from(users)\n *   .orderBy(desc(users.createdAt));\n * ```\n *\n * @see asc to sort in ascending order\n */\nexport function desc(column: AnyColumn | SQLWrapper): SQL {\n\treturn sql`${column} desc`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,iBAAoB;AAoBb,SAAS,IAAI,QAAqC;AACxD,SAAO,iBAAM,MAAM;AACpB;AAkBO,SAAS,KAAK,QAAqC;AACzD,SAAO,iBAAM,MAAM;AACpB;", "names": []}