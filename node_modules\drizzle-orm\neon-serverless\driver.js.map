{"version": 3, "sources": ["../../src/neon-serverless/driver.ts"], "sourcesContent": ["import { neonConfig, Pool, type PoolConfig } from '@neondatabase/serverless';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport type { NeonClient, NeonQueryResultHKT } from './session.ts';\nimport { NeonSession } from './session.ts';\n\nexport interface NeonDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class NeonDriver {\n\tstatic readonly [entityKind]: string = 'NeonDriver';\n\n\tconstructor(\n\t\tprivate client: NeonClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: NeonDriverOptions = {},\n\t) {\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): NeonSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new NeonSession(this.client, this.dialect, schema, { logger: this.options.logger });\n\t}\n}\n\nexport class NeonDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<NeonQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'NeonServerlessDatabase';\n}\n\nfunction construct<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends NeonClient = NeonClient,\n>(\n\tclient: TClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): NeonDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tconst dialect = new PgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new NeonDriver(client, dialect, { logger });\n\tconst session = driver.createSession(schema);\n\tconst db = new NeonDatabase(dialect, session, schema as any) as NeonDatabase<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends NeonClient = Pool,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | PoolConfig;\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t\t& {\n\t\t\t\tws?: any;\n\t\t\t}\n\t\t),\n\t]\n): NeonDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = new Pool({\n\t\t\tconnectionString: params[0],\n\t\t});\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ws, ...drizzleConfig } = params[0] as {\n\t\t\tconnection?: PoolConfig | string;\n\t\t\tws?: any;\n\t\t\tclient?: TClient;\n\t\t} & DrizzleConfig<TSchema>;\n\n\t\tif (ws) {\n\t\t\tneonConfig.webSocketConstructor = ws;\n\t\t}\n\n\t\tif (client) return construct(client, drizzleConfig);\n\n\t\tconst instance = typeof connection === 'string'\n\t\t\t? new Pool({\n\t\t\t\tconnectionString: connection,\n\t\t\t})\n\t\t\t: new Pool(connection);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): NeonDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": "AAAA,SAAS,YAAY,YAA6B;AAClD,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAA6B,gBAAgB;AAE7C,SAAS,mBAAmB;AAMrB,MAAM,WAAW;AAAA,EAGvB,YACS,QACA,SACA,UAA6B,CAAC,GACrC;AAHO;AACA;AACA;AAAA,EAET;AAAA,EAPA,QAAiB,UAAU,IAAY;AAAA,EASvC,cACC,QAC+D;AAC/D,WAAO,IAAI,YAAY,KAAK,QAAQ,KAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,QAAQ,OAAO,CAAC;AAAA,EAC1F;AACD;AAEO,MAAM,qBAEH,WAAwC;AAAA,EACjD,QAA0B,UAAU,IAAY;AACjD;AAEA,SAAS,UAIR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,UAAU,EAAE,QAAQ,OAAO,OAAO,CAAC;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,WAAW,QAAQ,SAAS,EAAE,OAAO,CAAC;AACzD,QAAM,UAAU,OAAO,cAAc,MAAM;AAC3C,QAAM,KAAK,IAAI,aAAa,SAAS,SAAS,MAAa;AAC3D,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;AAEO,SAAS,WAIZ,QAoBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,WAAW,IAAI,KAAK;AAAA,MACzB,kBAAkB,OAAO,CAAC;AAAA,IAC3B,CAAC;AAED,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,IAAI,GAAG,cAAc,IAAI,OAAO,CAAC;AAM7D,QAAI,IAAI;AACP,iBAAW,uBAAuB;AAAA,IACnC;AAEA,QAAI;AAAQ,aAAO,UAAU,QAAQ,aAAa;AAElD,UAAM,WAAW,OAAO,eAAe,WACpC,IAAI,KAAK;AAAA,MACV,kBAAkB;AAAA,IACnB,CAAC,IACC,IAAI,KAAK,UAAU;AAEtB,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUA,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["drizzle"]}