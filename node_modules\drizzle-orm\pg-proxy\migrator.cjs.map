{"version": 3, "sources": ["../../src/pg-proxy/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { PgRemoteDatabase } from './driver.ts';\n\nexport type ProxyMigrator = (migrationQueries: string[]) => Promise<void>;\n\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: PgRemoteDatabase<TSchema>,\n\tcallback: ProxyMigrator,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\n\tconst migrationTableCreate = sql`\n\t\tCREATE TABLE IF NOT EXISTS \"drizzle\".\"__drizzle_migrations\" (\n\t\t\tid SERIAL PRIMARY KEY,\n\t\t\thash text NOT NULL,\n\t\t\tcreated_at numeric\n\t\t)\n\t`;\n\n\tawait db.execute(sql`CREATE SCHEMA IF NOT EXISTS \"drizzle\"`);\n\tawait db.execute(migrationTableCreate);\n\n\tconst dbMigrations = await db.execute<{\n\t\tid: number;\n\t\thash: string;\n\t\tcreated_at: string;\n\t}>(\n\t\tsql`SELECT id, hash, created_at FROM \"drizzle\".\"__drizzle_migrations\" ORDER BY created_at DESC LIMIT 1`,\n\t);\n\n\tconst lastDbMigration = dbMigrations[0] ?? undefined;\n\n\tconst queriesToRun: string[] = [];\n\n\tfor (const migration of migrations) {\n\t\tif (\n\t\t\t!lastDbMigration\n\t\t\t|| Number(lastDbMigration.created_at)! < migration.folderMillis\n\t\t) {\n\t\t\tqueriesToRun.push(\n\t\t\t\t...migration.sql,\n\t\t\t\t`INSERT INTO \"drizzle\".\"__drizzle_migrations\" (\"hash\", \"created_at\") VALUES('${migration.hash}', '${migration.folderMillis}')`,\n\t\t\t);\n\t\t}\n\t}\n\n\tawait callback(queriesToRun);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAmC;AACnC,iBAAoB;AAKpB,eAAsB,QACrB,IACA,UACA,QACC;AACD,QAAM,iBAAa,oCAAmB,MAAM;AAE5C,QAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQ7B,QAAM,GAAG,QAAQ,qDAA0C;AAC3D,QAAM,GAAG,QAAQ,oBAAoB;AAErC,QAAM,eAAe,MAAM,GAAG;AAAA,IAK7B;AAAA,EACD;AAEA,QAAM,kBAAkB,aAAa,CAAC,KAAK;AAE3C,QAAM,eAAyB,CAAC;AAEhC,aAAW,aAAa,YAAY;AACnC,QACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAK,UAAU,cAClD;AACD,mBAAa;AAAA,QACZ,GAAG,UAAU;AAAA,QACb,+EAA+E,UAAU,IAAI,OAAO,UAAU,YAAY;AAAA,MAC3H;AAAA,IACD;AAAA,EACD;AAEA,QAAM,SAAS,YAAY;AAC5B;", "names": []}