{"version": 3, "sources": ["../../src/planetscale-serverless/driver.ts"], "sourcesContent": ["import type { Config } from '@planetscale/database';\nimport { Client } from '@planetscale/database';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { MySqlDatabase } from '~/mysql-core/db.ts';\nimport { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport type { PlanetScalePreparedQueryHKT, PlanetscaleQueryResultHKT } from './session.ts';\nimport { PlanetscaleSession } from './session.ts';\n\nexport interface PlanetscaleSDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class PlanetScaleDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends MySqlDatabase<PlanetscaleQueryResultHKT, PlanetScalePreparedQueryHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PlanetScaleDatabase';\n}\n\nfunction construct<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Client = Client,\n>(\n\tclient: TClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): PlanetScaleDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\t// Client is not Drizzle Object, so we can ignore this rule here\n\t// eslint-disable-next-line no-instanceof/no-instanceof\n\tif (!(client instanceof Client)) {\n\t\tthrow new Error(`Warning: You need to pass an instance of Client:\n\nimport { Client } from \"@planetscale/database\";\n\nconst client = new Client({\n  host: process.env[\"DATABASE_HOST\"],\n  username: process.env[\"DATABASE_USERNAME\"],\n  password: process.env[\"DATABASE_PASSWORD\"],\n});\n\nconst db = drizzle(client);\n\t\t`);\n\t}\n\n\tconst dialect = new MySqlDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new PlanetscaleSession(client, dialect, undefined, schema, { logger });\n\tconst db = new PlanetScaleDatabase(dialect, session, schema as any, 'planetscale') as PlanetScaleDatabase<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Client = Client,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tDrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | Config;\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t),\n\t]\n): PlanetScaleDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = new Client({\n\t\t\turl: params[0],\n\t\t});\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& { connection?: Config | string; client?: TClient }\n\t\t\t& DrizzleConfig;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tconst instance = typeof connection === 'string'\n\t\t\t? new Client({\n\t\t\t\turl: connection,\n\t\t\t})\n\t\t\t: new Client(\n\t\t\t\tconnection!,\n\t\t\t);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): PlanetScaleDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAuB;AACvB,oBAA2B;AAE3B,oBAA8B;AAC9B,gBAA8B;AAC9B,qBAA6B;AAC7B,uBAKO;AACP,mBAA6C;AAE7C,qBAAmC;AAM5B,MAAM,4BAEH,wBAA+E;AAAA,EACxF,QAA0B,wBAAU,IAAY;AACjD;AAEA,SAAS,UAIR,QACA,SAAiC,CAAC,GAGjC;AAGD,MAAI,EAAE,kBAAkB,yBAAS;AAChC,UAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWf;AAAA,EACF;AAEA,QAAM,UAAU,IAAI,4BAAa,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC1D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,kCAAmB,QAAQ,SAAS,QAAW,QAAQ,EAAE,OAAO,CAAC;AACrF,QAAM,KAAK,IAAI,oBAAoB,SAAS,SAAS,QAAe,aAAa;AACjF,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;AAEO,SAAS,WAIZ,QAiBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,WAAW,IAAI,uBAAO;AAAA,MAC3B,KAAK,OAAO,CAAC;AAAA,IACd,CAAC;AAED,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,UAAI,uBAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAIzD,QAAI;AAAQ,aAAO,UAAU,QAAQ,aAAa;AAElD,UAAM,WAAW,OAAO,eAAe,WACpC,IAAI,uBAAO;AAAA,MACZ,KAAK;AAAA,IACN,CAAC,IACC,IAAI;AAAA,MACL;AAAA,IACD;AAED,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUA,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["drizzle"]}