{"version": 3, "sources": ["../../src/better-sqlite3/driver.ts"], "sourcesContent": ["import Client, { type Database, type Options, type RunResult } from 'better-sqlite3';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport { BetterSQLiteSession } from './session.ts';\n\nexport type DrizzleBetterSQLite3DatabaseConfig =\n\t| ({\n\t\tsource?:\n\t\t\t| string\n\t\t\t| Buffer;\n\t} & Options)\n\t| string\n\t| undefined;\n\nexport class BetterSQLite3Database<TSchema extends Record<string, unknown> = Record<string, never>>\n\textends BaseSQLiteDatabase<'sync', RunResult, TSchema>\n{\n\tstatic override readonly [entityKind]: string = 'BetterSQLite3Database';\n}\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: Database,\n\tconfig: DrizzleConfig<TSchema> = {},\n): BetterSQLite3Database<TSchema> & {\n\t$client: Database;\n} {\n\tconst dialect = new SQLiteSyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new BetterSQLiteSession(client, dialect, schema, { logger });\n\tconst db = new BetterSQLite3Database('sync', dialect, session, schema);\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n>(\n\t...params:\n\t\t| []\n\t\t| [\n\t\t\tDatabase | string,\n\t\t]\n\t\t| [\n\t\t\tDatabase | string,\n\t\t\tDrizzleConfig<TSchema>,\n\t\t]\n\t\t| [\n\t\t\t(\n\t\t\t\t& DrizzleConfig<TSchema>\n\t\t\t\t& ({\n\t\t\t\t\tconnection?: DrizzleBetterSQLite3DatabaseConfig;\n\t\t\t\t} | {\n\t\t\t\t\tclient: Database;\n\t\t\t\t})\n\t\t\t),\n\t\t]\n): BetterSQLite3Database<TSchema> & {\n\t$client: Database;\n} {\n\tif (params[0] === undefined || typeof params[0] === 'string') {\n\t\tconst instance = params[0] === undefined ? new Client() : new Client(params[0]);\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& {\n\t\t\t\tconnection?: DrizzleBetterSQLite3DatabaseConfig;\n\t\t\t\tclient?: Database;\n\t\t\t}\n\t\t\t& DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tif (typeof connection === 'object') {\n\t\t\tconst { source, ...options } = connection;\n\n\t\t\tconst instance = new Client(source, options);\n\n\t\t\treturn construct(instance, drizzleConfig) as any;\n\t\t}\n\n\t\tconst instance = new Client(connection);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as Database, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): BetterSQLite3Database<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": "AAAA,OAAO,YAA6D;AACpE,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAAS,0BAA0B;AACnC,SAAS,yBAAyB;AAClC,SAA6B,gBAAgB;AAC7C,SAAS,2BAA2B;AAW7B,MAAM,8BACJ,mBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AACjD;AAEA,SAAS,UACR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,kBAAkB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC/D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,oBAAoB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AAC3E,QAAM,KAAK,IAAI,sBAAsB,QAAQ,SAAS,SAAS,MAAM;AACrE,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;AAEO,SAAS,WAGZ,QAqBF;AACD,MAAI,OAAO,CAAC,MAAM,UAAa,OAAO,OAAO,CAAC,MAAM,UAAU;AAC7D,UAAM,WAAW,OAAO,CAAC,MAAM,SAAY,IAAI,OAAO,IAAI,IAAI,OAAO,OAAO,CAAC,CAAC;AAE9E,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAOzD,QAAI;AAAQ,aAAO,UAAU,QAAQ,aAAa;AAElD,QAAI,OAAO,eAAe,UAAU;AACnC,YAAM,EAAE,QAAQ,GAAG,QAAQ,IAAI;AAE/B,YAAMA,YAAW,IAAI,OAAO,QAAQ,OAAO;AAE3C,aAAO,UAAUA,WAAU,aAAa;AAAA,IACzC;AAEA,UAAM,WAAW,IAAI,OAAO,UAAU;AAEtC,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAe,OAAO,CAAC,CAAuC;AACxF;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["instance", "drizzle"]}