{"version": 3, "sources": ["../../src/singlestore-proxy/session.ts"], "sourcesContent": ["import type { FieldPacket, ResultSetHeader } from 'mysql2/promise';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { SingleStoreDialect } from '~/singlestore-core/dialect.ts';\nimport { SingleStoreTransaction } from '~/singlestore-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/singlestore-core/query-builders/select.types.ts';\nimport type {\n\tPreparedQueryKind,\n\tSingleStorePreparedQueryConfig,\n\tSingleStorePreparedQueryHKT,\n\tSingleStoreQueryResultHKT,\n\tSingleStoreTransactionConfig,\n} from '~/singlestore-core/session.ts';\nimport { SingleStorePreparedQuery as PreparedQueryBase, SingleStoreSession } from '~/singlestore-core/session.ts';\nimport type { Query, SQL } from '~/sql/sql.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\nimport type { RemoteCallback } from './driver.ts';\n\nexport type SingleStoreRawQueryResult = [ResultSetHeader, FieldPacket[]];\n\nexport interface SingleStoreRemoteSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class SingleStoreRemoteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SingleStoreSession<SingleStoreRemoteQueryResultHKT, SingleStoreRemotePreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreRemoteSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tdialect: SingleStoreDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: SingleStoreRemoteSessionOptions,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends SingleStorePreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tgeneratedIds?: Record<string, unknown>[],\n\t\treturningIds?: SelectedFieldsOrdered,\n\t): PreparedQueryKind<SingleStoreRemotePreparedQueryHKT, T> {\n\t\treturn new PreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tcustomResultMapper,\n\t\t\tgeneratedIds,\n\t\t\treturningIds,\n\t\t) as PreparedQueryKind<SingleStoreRemotePreparedQueryHKT, T>;\n\t}\n\n\toverride all<T = unknown>(query: SQL): Promise<T[]> {\n\t\tconst querySql = this.dialect.sqlToQuery(query);\n\t\tthis.logger.logQuery(querySql.sql, querySql.params);\n\t\treturn this.client(querySql.sql, querySql.params, 'all').then(({ rows }) => rows) as Promise<T[]>;\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: SingleStoreProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t_config?: SingleStoreTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the SingleStore Proxy driver');\n\t}\n}\n\nexport class SingleStoreProxyTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SingleStoreTransaction<\n\tSingleStoreRemoteQueryResultHKT,\n\tSingleStoreRemotePreparedQueryHKT,\n\tTFullSchema,\n\tTSchema\n> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreProxyTransaction';\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: SingleStoreProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the SingleStore Proxy driver');\n\t}\n}\n\nexport class PreparedQuery<T extends SingleStorePreparedQueryConfig> extends PreparedQueryBase<T> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreProxyPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\t// Keys that were used in $default and the value that was generated for them\n\t\tprivate generatedIds?: Record<string, unknown>[],\n\t\t// Keys that should be returned, it has the column with all properries + key from object\n\t\tprivate returningIds?: SelectedFieldsOrdered,\n\t) {\n\t\tsuper();\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tconst { fields, client, queryString, logger, joinsNotNullableMap, customResultMapper, returningIds, generatedIds } =\n\t\t\tthis;\n\n\t\tlogger.logQuery(queryString, params);\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst { rows: data } = await client(queryString, params, 'execute');\n\n\t\t\tconst insertId = data[0].insertId as number;\n\t\t\tconst affectedRows = data[0].affectedRows;\n\n\t\t\tif (returningIds) {\n\t\t\t\tconst returningResponse = [];\n\t\t\t\tlet j = 0;\n\t\t\t\tfor (let i = insertId; i < insertId + affectedRows; i++) {\n\t\t\t\t\tfor (const column of returningIds) {\n\t\t\t\t\t\tconst key = returningIds[0]!.path[0]!;\n\t\t\t\t\t\tif (is(column.field, Column)) {\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\tif (column.field.primary && column.field.autoIncrement) {\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: i });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (column.field.defaultFn && generatedIds) {\n\t\t\t\t\t\t\t\t// generatedIds[rowIdx][key]\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: generatedIds[j]![key] });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tj++;\n\t\t\t\t}\n\n\t\t\t\treturn returningResponse;\n\t\t\t}\n\n\t\t\treturn data;\n\t\t}\n\n\t\tconst { rows } = await client(queryString, params, 'all');\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\toverride iterator(\n\t\t_placeholderValues: Record<string, unknown> = {},\n\t): AsyncGenerator<T['iterator']> {\n\t\tthrow new Error('Streaming is not supported by the SingleStore Proxy driver');\n\t}\n}\n\nexport interface SingleStoreRemoteQueryResultHKT extends SingleStoreQueryResultHKT {\n\ttype: SingleStoreRawQueryResult;\n}\n\nexport interface SingleStoreRemotePreparedQueryHKT extends SingleStorePreparedQueryHKT {\n\ttype: PreparedQuery<Assume<this['config'], SingleStorePreparedQueryConfig>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAuB;AACvB,oBAA+B;AAE/B,oBAA2B;AAG3B,8BAAuC;AASvC,qBAAkF;AAElF,iBAAiC;AACjC,mBAA0C;AASnC,MAAM,iCAGH,kCAA6G;AAAA,EAKtH,YACS,QACR,SACQ,QACR,SACC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EAYR,aACC,OACA,QACA,oBACA,cACA,cAC0D;AAC1D,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,IAAiB,OAA0B;AACnD,UAAM,WAAW,KAAK,QAAQ,WAAW,KAAK;AAC9C,SAAK,OAAO,SAAS,SAAS,KAAK,SAAS,MAAM;AAClD,WAAO,KAAK,OAAO,SAAS,KAAK,SAAS,QAAQ,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,IAAI;AAAA,EACjF;AAAA,EAEA,MAAe,YACd,cACA,SACa;AACb,UAAM,IAAI,MAAM,gEAAgE;AAAA,EACjF;AACD;AAEO,MAAM,oCAGH,+CAKR;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,MAAe,YACd,cACa;AACb,UAAM,IAAI,MAAM,gEAAgE;AAAA,EACjF;AACD;AAEO,MAAM,sBAAgE,eAAAA,yBAAqB;AAAA,EAGjG,YACS,QACA,aACA,QACA,QACA,QACA,oBAEA,cAEA,cACP;AACD,UAAM;AAXE;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAAA,EAGT;AAAA,EAfA,QAA0B,wBAAU,IAAY;AAAA,EAiBhD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,UAAM,EAAE,QAAQ,QAAQ,aAAa,QAAQ,qBAAqB,oBAAoB,cAAc,aAAa,IAChH;AAED,WAAO,SAAS,aAAa,MAAM;AAEnC,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,EAAE,MAAM,KAAK,IAAI,MAAM,OAAO,aAAa,QAAQ,SAAS;AAElE,YAAM,WAAW,KAAK,CAAC,EAAE;AACzB,YAAM,eAAe,KAAK,CAAC,EAAE;AAE7B,UAAI,cAAc;AACjB,cAAM,oBAAoB,CAAC;AAC3B,YAAI,IAAI;AACR,iBAAS,IAAI,UAAU,IAAI,WAAW,cAAc,KAAK;AACxD,qBAAW,UAAU,cAAc;AAClC,kBAAM,MAAM,aAAa,CAAC,EAAG,KAAK,CAAC;AACnC,oBAAI,kBAAG,OAAO,OAAO,oBAAM,GAAG;AAE7B,kBAAI,OAAO,MAAM,WAAW,OAAO,MAAM,eAAe;AACvD,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AAAA,cACpC;AACA,kBAAI,OAAO,MAAM,aAAa,cAAc;AAE3C,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,EAAG,GAAG,EAAE,CAAC;AAAA,cACxD;AAAA,YACD;AAAA,UACD;AACA;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,EAAE,KAAK,IAAI,MAAM,OAAO,aAAa,QAAQ,KAAK;AAExD,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACvF;AAAA,EAES,SACR,qBAA8C,CAAC,GACf;AAChC,UAAM,IAAI,MAAM,4DAA4D;AAAA,EAC7E;AACD;", "names": ["PreparedQueryBase"]}