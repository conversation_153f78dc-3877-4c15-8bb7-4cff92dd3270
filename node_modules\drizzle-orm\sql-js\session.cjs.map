{"version": 3, "sources": ["../../src/sql-js/session.ts"], "sourcesContent": ["import type { BindParams, Database, Statement } from 'sql.js';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport type {\n\tPreparedQueryConfig as PreparedQueryConfigBase,\n\tSQLiteExecuteMethod,\n\tSQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { SQLitePreparedQuery as PreparedQueryBase, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface SQLJsSessionOptions {\n\tlogger?: Logger;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class SQLJsSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'sync', void, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLJsSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: Database,\n\t\tdialect: SQLiteSyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: SQLJsSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t): PreparedQuery<T> {\n\t\tconst stmt = this.client.prepare(query.sql);\n\t\treturn new PreparedQuery(stmt, query, this.logger, fields, executeMethod, isResponseInArrayMode);\n\t}\n\n\toverride prepareOneTimeQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t): PreparedQuery<T> {\n\t\tconst stmt = this.client.prepare(query.sql);\n\t\treturn new PreparedQuery(\n\t\t\tstmt,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t\ttrue,\n\t\t);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: SQLJsTransaction<TFullSchema, TSchema>) => T,\n\t\tconfig: SQLiteTransactionConfig = {},\n\t): T {\n\t\tconst tx = new SQLJsTransaction('sync', this.dialect, this, this.schema);\n\t\tthis.run(sql.raw(`begin${config.behavior ? ` ${config.behavior}` : ''}`));\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\tthis.run(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tthis.run(sql`rollback`);\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class SQLJsTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'sync', void, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLJsTransaction';\n\n\toverride transaction<T>(transaction: (tx: SQLJsTransaction<TFullSchema, TSchema>) => T): T {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new SQLJsTransaction('sync', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\ttx.run(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\ttx.run(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\ttx.run(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class PreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends PreparedQueryBase<\n\t{ type: 'sync'; run: void; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLJsPreparedQuery';\n\n\tconstructor(\n\t\tprivate stmt: Statement,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => unknown,\n\t\tprivate isOneTimeQuery = false,\n\t) {\n\t\tsuper('sync', executeMethod, query);\n\t}\n\n\trun(placeholderValues?: Record<string, unknown>): void {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\tconst result = this.stmt.run(params as BindParams);\n\n\t\tif (this.isOneTimeQuery) {\n\t\t\tthis.free();\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tall(placeholderValues?: Record<string, unknown>): T['all'] {\n\t\tconst { fields, joinsNotNullableMap, logger, query, stmt, isOneTimeQuery, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\t\t\tstmt.bind(params as BindParams);\n\t\t\tconst rows: unknown[] = [];\n\t\t\twhile (stmt.step()) {\n\t\t\t\trows.push(stmt.getAsObject());\n\t\t\t}\n\n\t\t\tif (isOneTimeQuery) {\n\t\t\t\tthis.free();\n\t\t\t}\n\n\t\t\treturn rows;\n\t\t}\n\n\t\tconst rows = this.values(placeholderValues) as unknown[][];\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows, normalizeFieldValue) as T['all'];\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow(fields!, row.map((v) => normalizeFieldValue(v)), joinsNotNullableMap));\n\t}\n\n\tget(placeholderValues?: Record<string, unknown>): T['get'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst { fields, stmt, isOneTimeQuery, joinsNotNullableMap, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst result = stmt.getAsObject(params as BindParams);\n\n\t\t\tif (isOneTimeQuery) {\n\t\t\t\tthis.free();\n\t\t\t}\n\n\t\t\treturn result;\n\t\t}\n\n\t\tconst row = stmt.get(params as BindParams);\n\n\t\tif (isOneTimeQuery) {\n\t\t\tthis.free();\n\t\t}\n\n\t\tif (!row || (row.length === 0 && fields!.length > 0)) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper([row], normalizeFieldValue) as T['get'];\n\t\t}\n\n\t\treturn mapResultRow(fields!, row.map((v) => normalizeFieldValue(v)), joinsNotNullableMap);\n\t}\n\n\tvalues(placeholderValues?: Record<string, unknown>): T['values'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\tthis.stmt.bind(params as BindParams);\n\t\tconst rows: unknown[] = [];\n\t\twhile (this.stmt.step()) {\n\t\t\trows.push(this.stmt.get());\n\t\t}\n\n\t\tif (this.isOneTimeQuery) {\n\t\t\tthis.free();\n\t\t}\n\n\t\treturn rows;\n\t}\n\n\tfree(): boolean {\n\t\treturn this.stmt.free();\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nfunction normalizeFieldValue(value: unknown) {\n\tif (value instanceof Uint8Array) { // eslint-disable-line no-instanceof/no-instanceof\n\t\tif (typeof Buffer !== 'undefined') {\n\t\t\tif (!(value instanceof Buffer)) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\treturn Buffer.from(value);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\t\tif (typeof TextDecoder !== 'undefined') {\n\t\t\treturn new TextDecoder().decode(value);\n\t\t}\n\t\tthrow new Error('TextDecoder is not available. Please provide either Buffer or TextDecoder polyfill.');\n\t}\n\treturn value;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAE3B,oBAA2B;AAE3B,iBAAkD;AAElD,yBAAkC;AAOlC,qBAAwE;AACxE,mBAA6B;AAQtB,MAAM,qBAGH,6BAAkD;AAAA,EAK3D,YACS,QACR,SACQ,QACR,UAA+B,CAAC,GAC/B;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EAYR,aACC,OACA,QACA,eACA,uBACmB;AACnB,UAAM,OAAO,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC1C,WAAO,IAAI,cAAc,MAAM,OAAO,KAAK,QAAQ,QAAQ,eAAe,qBAAqB;AAAA,EAChG;AAAA,EAES,oBACR,OACA,QACA,eACA,uBACA,oBACmB;AACnB,UAAM,OAAO,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC1C,WAAO,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,YACR,aACA,SAAkC,CAAC,GAC/B;AACJ,UAAM,KAAK,IAAI,iBAAiB,QAAQ,KAAK,SAAS,MAAM,KAAK,MAAM;AACvE,SAAK,IAAI,eAAI,IAAI,QAAQ,OAAO,WAAW,IAAI,OAAO,QAAQ,KAAK,EAAE,EAAE,CAAC;AACxE,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,IAAI,sBAAW;AACpB,aAAO;AAAA,IACR,SAAS,KAAK;AACb,WAAK,IAAI,wBAAa;AACtB,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,yBAGH,qCAAsD;AAAA,EAC/D,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAe,aAAmE;AAC1F,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI,iBAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AACrG,OAAG,IAAI,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AAC5C,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,SAAG,IAAI,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AACpD,aAAO;AAAA,IACR,SAAS,KAAK;AACb,SAAG,IAAI,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AACxD,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,sBAA2E,eAAAA,oBAEtF;AAAA,EAGD,YACS,MACR,OACQ,QACA,QACR,eACQ,wBACA,oBACA,iBAAiB,OACxB;AACD,UAAM,QAAQ,eAAe,KAAK;AAT1B;AAEA;AACA;AAEA;AACA;AACA;AAAA,EAGT;AAAA,EAbA,QAA0B,wBAAU,IAAY;AAAA,EAehD,IAAI,mBAAmD;AACtD,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,UAAM,SAAS,KAAK,KAAK,IAAI,MAAoB;AAEjD,QAAI,KAAK,gBAAgB;AACxB,WAAK,KAAK;AAAA,IACX;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,EAAE,QAAQ,qBAAqB,QAAQ,OAAO,MAAM,gBAAgB,mBAAmB,IAAI;AACjG,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,aAAS,6BAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,WAAK,KAAK,MAAoB;AAC9B,YAAMC,QAAkB,CAAC;AACzB,aAAO,KAAK,KAAK,GAAG;AACnB,QAAAA,MAAK,KAAK,KAAK,YAAY,CAAC;AAAA,MAC7B;AAEA,UAAI,gBAAgB;AACnB,aAAK,KAAK;AAAA,MACX;AAEA,aAAOA;AAAA,IACR;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAE1C,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,MAAM,mBAAmB;AAAA,IACpD;AAEA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAAa,QAAS,IAAI,IAAI,CAAC,MAAM,oBAAoB,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAAA,EAC5G;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,MAAM,gBAAgB,qBAAqB,mBAAmB,IAAI;AAClF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,SAAS,KAAK,YAAY,MAAoB;AAEpD,UAAI,gBAAgB;AACnB,aAAK,KAAK;AAAA,MACX;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,MAAM,KAAK,IAAI,MAAoB;AAEzC,QAAI,gBAAgB;AACnB,WAAK,KAAK;AAAA,IACX;AAEA,QAAI,CAAC,OAAQ,IAAI,WAAW,KAAK,OAAQ,SAAS,GAAI;AACrD,aAAO;AAAA,IACR;AAEA,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,CAAC,GAAG,GAAG,mBAAmB;AAAA,IACrD;AAEA,eAAO,2BAAa,QAAS,IAAI,IAAI,CAAC,MAAM,oBAAoB,CAAC,CAAC,GAAG,mBAAmB;AAAA,EACzF;AAAA,EAEA,OAAO,mBAA0D;AAChE,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,SAAK,KAAK,KAAK,MAAoB;AACnC,UAAM,OAAkB,CAAC;AACzB,WAAO,KAAK,KAAK,KAAK,GAAG;AACxB,WAAK,KAAK,KAAK,KAAK,IAAI,CAAC;AAAA,IAC1B;AAEA,QAAI,KAAK,gBAAgB;AACxB,WAAK,KAAK;AAAA,IACX;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,OAAgB;AACf,WAAO,KAAK,KAAK,KAAK;AAAA,EACvB;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAEA,SAAS,oBAAoB,OAAgB;AAC5C,MAAI,iBAAiB,YAAY;AAChC,QAAI,OAAO,WAAW,aAAa;AAClC,UAAI,EAAE,iBAAiB,SAAS;AAC/B,eAAO,OAAO,KAAK,KAAK;AAAA,MACzB;AACA,aAAO;AAAA,IACR;AACA,QAAI,OAAO,gBAAgB,aAAa;AACvC,aAAO,IAAI,YAAY,EAAE,OAAO,KAAK;AAAA,IACtC;AACA,UAAM,IAAI,MAAM,qFAAqF;AAAA,EACtG;AACA,SAAO;AACR;", "names": ["PreparedQueryBase", "rows"]}