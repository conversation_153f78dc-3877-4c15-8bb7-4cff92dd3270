{"version": 3, "sources": ["../src/table.ts"], "sourcesContent": ["import type { Column, GetColumnData } from './column.ts';\nimport { entityKind } from './entity.ts';\nimport type { OptionalKeyOnly, RequiredKeyOnly } from './operations.ts';\nimport type { ExtraConfigColumn } from './pg-core/index.ts';\nimport type { SQLWrapper } from './sql/sql.ts';\nimport { TableName } from './table.utils.ts';\nimport type { Simplify, Update } from './utils.ts';\n\nexport interface TableConfig<TColumn extends Column = Column<any>> {\n\tname: string;\n\tschema: string | undefined;\n\tcolumns: Record<string, TColumn>;\n\tdialect: string;\n}\n\nexport type UpdateTableConfig<T extends TableConfig, TUpdate extends Partial<TableConfig>> = Required<\n\tUpdate<T, TUpdate>\n>;\n\n/** @internal */\nexport const Schema = Symbol.for('drizzle:Schema');\n\n/** @internal */\nexport const Columns = Symbol.for('drizzle:Columns');\n\n/** @internal */\nexport const ExtraConfigColumns = Symbol.for('drizzle:ExtraConfigColumns');\n\n/** @internal */\nexport const OriginalName = Symbol.for('drizzle:OriginalName');\n\n/** @internal */\nexport const BaseName = Symbol.for('drizzle:BaseName');\n\n/** @internal */\nexport const IsAlias = Symbol.for('drizzle:IsAlias');\n\n/** @internal */\nexport const ExtraConfigBuilder = Symbol.for('drizzle:ExtraConfigBuilder');\n\nconst IsDrizzleTable = Symbol.for('drizzle:IsDrizzleTable');\n\nexport interface Table<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends TableConfig = TableConfig,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n\nexport class Table<T extends TableConfig = TableConfig> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Table';\n\n\tdeclare readonly _: {\n\t\treadonly brand: 'Table';\n\t\treadonly config: T;\n\t\treadonly name: T['name'];\n\t\treadonly schema: T['schema'];\n\t\treadonly columns: T['columns'];\n\t\treadonly inferSelect: InferSelectModel<Table<T>>;\n\t\treadonly inferInsert: InferInsertModel<Table<T>>;\n\t};\n\n\tdeclare readonly $inferSelect: InferSelectModel<Table<T>>;\n\tdeclare readonly $inferInsert: InferInsertModel<Table<T>>;\n\n\t/** @internal */\n\tstatic readonly Symbol = {\n\t\tName: TableName as typeof TableName,\n\t\tSchema: Schema as typeof Schema,\n\t\tOriginalName: OriginalName as typeof OriginalName,\n\t\tColumns: Columns as typeof Columns,\n\t\tExtraConfigColumns: ExtraConfigColumns as typeof ExtraConfigColumns,\n\t\tBaseName: BaseName as typeof BaseName,\n\t\tIsAlias: IsAlias as typeof IsAlias,\n\t\tExtraConfigBuilder: ExtraConfigBuilder as typeof ExtraConfigBuilder,\n\t};\n\n\t/**\n\t * @internal\n\t * Can be changed if the table is aliased.\n\t */\n\t[TableName]: string;\n\n\t/**\n\t * @internal\n\t * Used to store the original name of the table, before any aliasing.\n\t */\n\t[OriginalName]: string;\n\n\t/** @internal */\n\t[Schema]: string | undefined;\n\n\t/** @internal */\n\t[Columns]!: T['columns'];\n\n\t/** @internal */\n\t[ExtraConfigColumns]!: Record<string, ExtraConfigColumn>;\n\n\t/**\n\t *  @internal\n\t * Used to store the table name before the transformation via the `tableCreator` functions.\n\t */\n\t[BaseName]: string;\n\n\t/** @internal */\n\t[IsAlias] = false;\n\n\t/** @internal */\n\t[IsDrizzleTable] = true;\n\n\t/** @internal */\n\t[ExtraConfigBuilder]: ((self: any) => Record<string, unknown> | unknown[]) | undefined = undefined;\n\n\tconstructor(name: string, schema: string | undefined, baseName: string) {\n\t\tthis[TableName] = this[OriginalName] = name;\n\t\tthis[Schema] = schema;\n\t\tthis[BaseName] = baseName;\n\t}\n}\n\nexport function isTable(table: unknown): table is Table {\n\treturn typeof table === 'object' && table !== null && IsDrizzleTable in table;\n}\n\n/**\n * Any table with a specified boundary.\n *\n * @example\n\t```ts\n\t// Any table with a specific name\n\ttype AnyUsersTable = AnyTable<{ name: 'users' }>;\n\t```\n *\n * To describe any table with any config, simply use `Table` without any type arguments, like this:\n *\n\t```ts\n\tfunction needsTable(table: Table) {\n\t\t...\n\t}\n\t```\n */\nexport type AnyTable<TPartial extends Partial<TableConfig>> = Table<UpdateTableConfig<TableConfig, TPartial>>;\n\nexport function getTableName<T extends Table>(table: T): T['_']['name'] {\n\treturn table[TableName];\n}\n\nexport function getTableUniqueName<T extends Table>(table: T): `${T['_']['schema']}.${T['_']['name']}` {\n\treturn `${table[Schema] ?? 'public'}.${table[TableName]}`;\n}\n\nexport type MapColumnName<TName extends string, TColumn extends Column, TDBColumNames extends boolean> =\n\tTDBColumNames extends true ? TColumn['_']['name']\n\t\t: TName;\n\nexport type InferModelFromColumns<\n\tTColumns extends Record<string, Column>,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean; override?: boolean } = { dbColumnNames: false; override: false },\n> = Simplify<\n\tTInferMode extends 'insert' ?\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as RequiredKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key]\n\t\t\t\t\t>\n\t\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t\t}\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as OptionalKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key],\n\t\t\t\t\t\tTConfig['override']\n\t\t\t\t\t>\n\t\t\t\t]?: GetColumnData<TColumns[Key], 'query'> | undefined;\n\t\t\t}\n\t\t: {\n\t\t\t[\n\t\t\t\tKey in keyof TColumns & string as MapColumnName<\n\t\t\t\t\tKey,\n\t\t\t\t\tTColumns[Key],\n\t\t\t\t\tTConfig['dbColumnNames']\n\t\t\t\t>\n\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t}\n>;\n\n/** @deprecated Use one of the alternatives: {@link InferSelectModel} / {@link InferInsertModel}, or `table.$inferSelect` / `table.$inferInsert`\n */\nexport type InferModel<\n\tTTable extends Table,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], TInferMode, TConfig>;\n\nexport type InferSelectModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'select', TConfig>;\n\nexport type InferInsertModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean; override?: boolean } = { dbColumnNames: false; override: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'insert', TConfig>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAI3B,yBAA0B;AAenB,MAAM,SAAS,OAAO,IAAI,gBAAgB;AAG1C,MAAM,UAAU,OAAO,IAAI,iBAAiB;AAG5C,MAAM,qBAAqB,OAAO,IAAI,4BAA4B;AAGlE,MAAM,eAAe,OAAO,IAAI,sBAAsB;AAGtD,MAAM,WAAW,OAAO,IAAI,kBAAkB;AAG9C,MAAM,UAAU,OAAO,IAAI,iBAAiB;AAG5C,MAAM,qBAAqB,OAAO,IAAI,4BAA4B;AAEzE,MAAM,iBAAiB,OAAO,IAAI,wBAAwB;AASnD,MAAM,MAAiE;AAAA,EAC7E,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAgBvC,OAAgB,SAAS;AAAA,IACxB,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,CAAC,4BAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV,CAAC,YAAY;AAAA;AAAA,EAGb,CAAC,MAAM;AAAA;AAAA,EAGP,CAAC,OAAO;AAAA;AAAA,EAGR,CAAC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnB,CAAC,QAAQ;AAAA;AAAA,EAGT,CAAC,OAAO,IAAI;AAAA;AAAA,EAGZ,CAAC,cAAc,IAAI;AAAA;AAAA,EAGnB,CAAC,kBAAkB,IAAsE;AAAA,EAEzF,YAAY,MAAc,QAA4B,UAAkB;AACvE,SAAK,4BAAS,IAAI,KAAK,YAAY,IAAI;AACvC,SAAK,MAAM,IAAI;AACf,SAAK,QAAQ,IAAI;AAAA,EAClB;AACD;AAEO,SAAS,QAAQ,OAAgC;AACvD,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,kBAAkB;AACzE;AAqBO,SAAS,aAA8B,OAA0B;AACvE,SAAO,MAAM,4BAAS;AACvB;AAEO,SAAS,mBAAoC,OAAmD;AACtG,SAAO,GAAG,MAAM,MAAM,KAAK,QAAQ,IAAI,MAAM,4BAAS,CAAC;AACxD;", "names": []}