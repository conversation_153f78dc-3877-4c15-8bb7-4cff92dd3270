{"version": 3, "sources": ["../../src/pg-proxy/driver.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { type PgRemoteQueryResultHKT, PgRemoteSession } from './session.ts';\n\nexport class PgRemoteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<PgRemoteQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PgRemoteDatabase';\n}\n\nexport type RemoteCallback = (\n\tsql: string,\n\tparams: any[],\n\tmethod: 'all' | 'execute',\n\ttypings?: any[],\n) => Promise<{ rows: any[] }>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tcallback: RemoteCallback,\n\tconfig: DrizzleConfig<TSchema> = {},\n\t_dialect: () => PgDialect = () => new PgDialect({ casing: config.casing }),\n): PgRemoteDatabase<TSchema> {\n\tconst dialect = _dialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new PgRemoteSession(callback, dialect, schema, { logger });\n\treturn new PgRemoteDatabase(dialect, session, schema as any) as PgRemoteDatabase<TSchema>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,oBAA8B;AAC9B,gBAA2B;AAC3B,qBAA0B;AAC1B,uBAKO;AAEP,qBAA6D;AAEtD,MAAM,yBAEH,qBAA4C;AAAA,EACrD,QAA0B,wBAAU,IAAY;AACjD;AASO,SAAS,QACf,UACA,SAAiC,CAAC,GAClC,WAA4B,MAAM,IAAI,yBAAU,EAAE,QAAQ,OAAO,OAAO,CAAC,GAC7C;AAC5B,QAAM,UAAU,SAAS;AACzB,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,+BAAgB,UAAU,SAAS,QAAQ,EAAE,OAAO,CAAC;AACzE,SAAO,IAAI,iBAAiB,SAAS,SAAS,MAAa;AAC5D;", "names": []}