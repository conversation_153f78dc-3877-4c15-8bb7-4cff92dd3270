{"version": 3, "sources": ["../../src/mysql2/driver.ts"], "sourcesContent": ["import { type Connection as CallbackConnection, createPool, type Pool as CallbackPool, type PoolOptions } from 'mysql2';\nimport type { Connection, Pool } from 'mysql2/promise';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { MySqlDatabase } from '~/mysql-core/db.ts';\nimport { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type { Mode } from '~/mysql-core/session.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport { DrizzleError } from '../errors.ts';\nimport type { MySql2Client, MySql2PreparedQueryHKT, MySql2QueryResultHKT } from './session.ts';\nimport { MySql2Session } from './session.ts';\n\nexport interface MySqlDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class MySql2Driver {\n\tstatic readonly [entityKind]: string = 'MySql2Driver';\n\n\tconstructor(\n\t\tprivate client: MySql2Client,\n\t\tprivate dialect: MySqlDialect,\n\t\tprivate options: MySqlDriverOptions = {},\n\t) {\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t\tmode: Mode,\n\t): MySql2Session<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new MySql2Session(this.client, this.dialect, schema, { logger: this.options.logger, mode });\n\t}\n}\n\nexport { MySqlDatabase } from '~/mysql-core/db.ts';\n\nexport class MySql2Database<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends MySqlDatabase<MySql2QueryResultHKT, MySql2PreparedQueryHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'MySql2Database';\n}\n\nexport type MySql2DrizzleConfig<TSchema extends Record<string, unknown> = Record<string, never>> =\n\t& Omit<DrizzleConfig<TSchema>, 'schema'>\n\t& ({ schema: TSchema; mode: Mode } | { schema?: undefined; mode?: Mode });\n\nfunction construct<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Pool | Connection | CallbackPool | CallbackConnection = CallbackPool,\n>(\n\tclient: TClient,\n\tconfig: MySql2DrizzleConfig<TSchema> = {},\n): MySql2Database<TSchema> & {\n\t$client: TClient;\n} {\n\tconst dialect = new MySqlDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tconst clientForInstance = isCallbackClient(client) ? client.promise() : client;\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tif (config.mode === undefined) {\n\t\t\tthrow new DrizzleError({\n\t\t\t\tmessage:\n\t\t\t\t\t'You need to specify \"mode\": \"planetscale\" or \"default\" when providing a schema. Read more: https://orm.drizzle.team/docs/rqb#modes',\n\t\t\t});\n\t\t}\n\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst mode = config.mode ?? 'default';\n\n\tconst driver = new MySql2Driver(clientForInstance as MySql2Client, dialect, { logger });\n\tconst session = driver.createSession(schema, mode);\n\tconst db = new MySql2Database(dialect, session, schema as any, mode) as MySql2Database<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n\ninterface CallbackClient {\n\tpromise(): MySql2Client;\n}\n\nfunction isCallbackClient(client: any): client is CallbackClient {\n\treturn typeof client.promise === 'function';\n}\n\nexport type AnyMySql2Connection = Pool | Connection | CallbackPool | CallbackConnection;\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends AnyMySql2Connection = CallbackPool,\n>(\n\t...params: [\n\t\tTClient | string,\n\t] | [\n\t\tTClient | string,\n\t\tMySql2DrizzleConfig<TSchema>,\n\t] | [\n\t\t(\n\t\t\t& MySql2DrizzleConfig<TSchema>\n\t\t\t& ({\n\t\t\t\tconnection: string | PoolOptions;\n\t\t\t} | {\n\t\t\t\tclient: TClient;\n\t\t\t})\n\t\t),\n\t]\n): MySql2Database<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst connectionString = params[0]!;\n\t\tconst instance = createPool({\n\t\t\turi: connectionString,\n\t\t});\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& { connection?: PoolOptions | string; client?: TClient }\n\t\t\t& MySql2DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tconst instance = typeof connection === 'string'\n\t\t\t? createPool({\n\t\t\t\turi: connection,\n\t\t\t})\n\t\t\t: createPool(connection!);\n\t\tconst db = construct(instance, drizzleConfig);\n\n\t\treturn db as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as MySql2DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: MySql2DrizzleConfig<TSchema>,\n\t): MySql2Database<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": "AAAA,SAAgD,kBAA+D;AAE/G,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAE7B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAA6B,gBAAgB;AAC7C,SAAS,oBAAoB;AAE7B,SAAS,qBAAqB;AAMvB,MAAM,aAAa;AAAA,EAGzB,YACS,QACA,SACA,UAA8B,CAAC,GACtC;AAHO;AACA;AACA;AAAA,EAET;AAAA,EAPA,QAAiB,UAAU,IAAY;AAAA,EASvC,cACC,QACA,MACiE;AACjE,WAAO,IAAI,cAAc,KAAK,QAAQ,KAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,QAAQ,QAAQ,KAAK,CAAC;AAAA,EAClG;AACD;AAEA,SAAS,iBAAAA,sBAAqB;AAEvB,MAAM,uBAEH,cAAqE;AAAA,EAC9E,QAA0B,UAAU,IAAY;AACjD;AAMA,SAAS,UAIR,QACA,SAAuC,CAAC,GAGvC;AACD,QAAM,UAAU,IAAI,aAAa,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC1D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,QAAM,oBAAoB,iBAAiB,MAAM,IAAI,OAAO,QAAQ,IAAI;AAExE,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,QAAI,OAAO,SAAS,QAAW;AAC9B,YAAM,IAAI,aAAa;AAAA,QACtB,SACC;AAAA,MACF,CAAC;AAAA,IACF;AAEA,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,OAAO,OAAO,QAAQ;AAE5B,QAAM,SAAS,IAAI,aAAa,mBAAmC,SAAS,EAAE,OAAO,CAAC;AACtF,QAAM,UAAU,OAAO,cAAc,QAAQ,IAAI;AACjD,QAAM,KAAK,IAAI,eAAe,SAAS,SAAS,QAAe,IAAI;AACnE,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;AAMA,SAAS,iBAAiB,QAAuC;AAChE,SAAO,OAAO,OAAO,YAAY;AAClC;AAIO,SAAS,WAIZ,QAiBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,mBAAmB,OAAO,CAAC;AACjC,UAAM,WAAW,WAAW;AAAA,MAC3B,KAAK;AAAA,IACN,CAAC;AAED,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAIzD,QAAI;AAAQ,aAAO,UAAU,QAAQ,aAAa;AAElD,UAAM,WAAW,OAAO,eAAe,WACpC,WAAW;AAAA,MACZ,KAAK;AAAA,IACN,CAAC,IACC,WAAW,UAAW;AACzB,UAAM,KAAK,UAAU,UAAU,aAAa;AAE5C,WAAO;AAAA,EACR;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAA6C;AAC7F;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["MySqlDatabase", "drizzle"]}